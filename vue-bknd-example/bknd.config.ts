import { registerLocalMediaAdapter } from "bknd/adapter/node";
import type { RuntimeBkndConfig } from "bknd/adapter";
import { boolean, em, entity, text } from "bknd";
import { secureRandomString } from "bknd/utils";

// 注册本地媒体适配器
const local = registerLocalMediaAdapter();

const schema = em({
   todos: entity("todos", {
      title: text(),
      done: boolean(),
   }),
});

// 注册 schema 以获得自动类型补全
type Database = (typeof schema)["DB"];
declare module "bknd" {
   interface DB extends Database {}
}

export default {
   app: (env) => ({
      connection: {
         url: env?.DB_URL ?? "file:data.db",
      },
   }),
   initialConfig: {
      data: schema.toJSON(),
      auth: {
         enabled: true,
         jwt: {
            issuer: "bknd-vue-example",
            secret: secureRandomString(64),
         },
      },
      media: {
         enabled: true,
         adapter: local({
            path: "./public/uploads",
         }),
      },
   },
   options: {
      seed: async (ctx) => {
         await ctx.em.mutator("todos").insertMany([
            { title: "Learn bknd with Vue", done: true },
            { title: "Build something cool", done: false },
         ]);

         await ctx.app.module.auth.createUser({
            email: "<EMAIL>",
            password: "12345678",
         });
      },
   },
} as const satisfies RuntimeBkndConfig<{ DB_URL?: string }>;
