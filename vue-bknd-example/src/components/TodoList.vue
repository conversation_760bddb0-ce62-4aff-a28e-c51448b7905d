<template>
  <div class="todo-list">
    <h2>Todo List</h2>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">Loading...</div>
    
    <!-- 错误状态 -->
    <div v-if="error" class="error">{{ error }}</div>
    
    <!-- 添加新 todo -->
    <form @submit.prevent="addTodo" class="add-todo">
      <input 
        v-model="newTodoTitle" 
        type="text" 
        placeholder="Add new todo..."
        required
      />
      <button type="submit">Add</button>
    </form>
    
    <!-- Todo 列表 -->
    <ul class="todos">
      <li v-for="todo in data" :key="todo.id" class="todo-item">
        <input 
          type="checkbox" 
          :checked="todo.done"
          @change="toggleTodo(todo)"
        />
        <span :class="{ completed: todo.done }">{{ todo.title }}</span>
        <button @click="deleteTodo(todo.id)" class="delete-btn">❌</button>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useEntity } from '../composables/useBknd'

interface Todo {
  id: number
  title: string
  done: boolean
}

const { data, loading, error, fetchData, createItem, updateItem, deleteItem } = useEntity<Todo>('todos')
const newTodoTitle = ref('')

onMounted(() => {
  fetchData()
})

const addTodo = async () => {
  if (newTodoTitle.value.trim()) {
    await createItem({ title: newTodoTitle.value, done: false })
    newTodoTitle.value = ''
  }
}

const toggleTodo = async (todo: Todo) => {
  await updateItem(todo.id, { done: !todo.done })
}

const deleteTodo = async (id: number) => {
  await deleteItem(id)
}
</script>

<style scoped>
.todo-list {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

.loading, .error {
  text-align: center;
  padding: 10px;
}

.error {
  color: red;
}

.add-todo {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.add-todo input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.add-todo button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.todos {
  list-style: none;
  padding: 0;
}

.todo-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 5px;
}

.completed {
  text-decoration: line-through;
  color: #888;
}

.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  margin-left: auto;
}
</style>
