// Vue 3 Composition API 封装 bknd 客户端
import { ref, reactive } from 'vue'
import { Api } from 'bknd/client'

// 创建 API 实例
const api = new Api({ 
  host: import.meta.env.VITE_BKND_HOST || 'http://localhost:3000' 
})

// 全局状态
const user = ref(null)
const isAuthenticated = ref(false)

export function useBknd() {
  return {
    api,
    user,
    isAuthenticated
  }
}

// 数据查询 composable
export function useEntity<T = any>(entityName: string) {
  const data = ref<T[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  const fetchData = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await api.data.readMany(entityName)
      data.value = response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
    } finally {
      loading.value = false
    }
  }

  const createItem = async (item: Partial<T>) => {
    try {
      const response = await api.data.createOne(entityName, item)
      data.value.push(response.data)
      return response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      throw err
    }
  }

  const updateItem = async (id: number, updates: Partial<T>) => {
    try {
      const response = await api.data.updateOne(entityName, id, updates)
      const index = data.value.findIndex((item: any) => item.id === id)
      if (index !== -1) {
        data.value[index] = response.data
      }
      return response.data
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      throw err
    }
  }

  const deleteItem = async (id: number) => {
    try {
      await api.data.deleteOne(entityName, id)
      data.value = data.value.filter((item: any) => item.id !== id)
    } catch (err) {
      error.value = err instanceof Error ? err.message : 'Unknown error'
      throw err
    }
  }

  return {
    data,
    loading,
    error,
    fetchData,
    createItem,
    updateItem,
    deleteItem
  }
}

// 认证 composable
export function useAuth() {
  const login = async (email: string, password: string) => {
    try {
      const response = await api.auth.login({ email, password })
      user.value = response.user
      isAuthenticated.value = true
      return response
    } catch (err) {
      throw err
    }
  }

  const logout = async () => {
    try {
      await api.auth.logout()
      user.value = null
      isAuthenticated.value = false
    } catch (err) {
      throw err
    }
  }

  const register = async (email: string, password: string) => {
    try {
      const response = await api.auth.register({ email, password })
      user.value = response.user
      isAuthenticated.value = true
      return response
    } catch (err) {
      throw err
    }
  }

  return {
    user,
    isAuthenticated,
    login,
    logout,
    register
  }
}
