import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { devServer } from 'bknd/adapter/vite'

export default defineConfig({
  plugins: [
    vue(),
    // 添加 bknd 开发服务器
    devServer({
      entry: './server.ts', // bknd 服务器入口
      exclude: [
        /.*\.vue($|\?)/,
        /.*\.ts($|\?)/,
        /^(?!.*\/__admin).*\.(s?css|less)($|\?)/,
        /^(?!.*\/api).*\.(ico|mp4|jpg|jpeg|svg|png|vtt|mp3|js)($|\?)/,
        /^\/@.+$/,
        /\/components.*?\.json.*/,
        /^\/(public|assets|static)\/.+/,
        /^\/node_modules\/.*/
      ]
    })
  ],
  server: {
    // 配置代理，将 API 请求转发到 bknd
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true
      },
      '/__admin': {
        target: 'http://localhost:3000',
        changeOrigin: true
      }
    }
  }
})
